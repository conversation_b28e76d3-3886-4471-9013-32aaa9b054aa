<template>
  <div class="medicine-info-container">
    <div class="info-header">
      <h3 class="section-title">一级药品</h3>
    </div>

    <div class="medicine-content">
      <BasicFormSearch :config="searchConfig" v-model:data="searchForm" @search="loadMedicineList" />
      <BasicTable @register="register" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable } from '@/components/BasicTable'
import BasicFormSearch from '@/components/BasicFormSearch/index.vue'
import { MedicineApi } from '@/api/system/smc/medicine'

const props = defineProps({
  cabinetId: {
    type: String,
    required: true
  }
})

const searchForm = ref<{ [key: string]: any }>({})

// 搜索配置
const searchConfig = {
  itemList: [
    {
      component: 'input',
      label: '药品名称',
      prop: 'medicineName',
      placeholder: '请输入药品名称'
    },
    {
      component: 'input',
      label: '药品编码',
      prop: 'medicineCode',
      placeholder: '请输入药品编码'
    },
    {
      component: 'input',
      label: '批次号',
      prop: 'batchNumber',
      placeholder: '请输入批次号'
    }
  ]
}

// 表格配置
const tableColumns = [
  { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
  {
    label: '药品名称',
    prop: 'medicineName',
    tooltip: true,
    minWidth: 150
  },
  {
    label: '药品编码',
    prop: 'medicineCode',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '规格',
    prop: 'specification',
    tooltip: true,
    minWidth: 100
  },
  {
    label: '批次号',
    prop: 'batchNumber',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '存放位置',
    prop: 'location',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '当前库存',
    prop: 'currentStock',
    tooltip: true,
    minWidth: 100
  },
  {
    label: '生产日期',
    prop: 'productionDate',
    dateFormate: 'YYYY-MM-DD',
    minWidth: 120
  },
  {
    label: '有效期',
    prop: 'expiryDate',
    dateFormate: 'YYYY-MM-DD',
    minWidth: 120
  },
  {
    label: '更新时间',
    prop: 'updateTime',
    dateFormate: 'YYYY-MM-DD HH:mm:ss',
    minWidth: 180
  }
]

const [register, { reload }] = useTable({
  api: MedicineApi.getMedicinePage,
  columns: tableColumns,
  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { 
      ...params, 
      ...searchForm.value,
      cabinetId: props.cabinetId 
    }
  }
})

const loadMedicineList = () => {
  reload()
}

onMounted(() => {
  loadMedicineList()
})
</script>

<style scoped lang="scss">
.medicine-info-container {
  .info-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .medicine-content {
    height: calc(100vh - 200px);
    overflow: auto;
  }
}
</style>
