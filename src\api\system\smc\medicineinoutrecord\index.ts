import request from '@/config/axios'

// 药品出入柜操作记录 VO
export interface MedicineInOutRecordVO {
}

// 药品出入柜操作记录 API
export const MedicineInOutRecordApi = {
  // 查询药品出入柜操作记录分页
  getMedicineInOutRecordPage: async (params: any) => {
    return await request.get({ url: `/system/medicine-in-out-record/page`, params })
  },
  // 查询药品出入柜操作记录列表
  getMedicineInOutRecordList: async (params) => {
    return await request.get({ url: `/system/medicine-in-out-record/list`, params })
  },

  // 查询药品出入柜操作记录详情
  getMedicineInOutRecord: async (params: any) => {
    return await request.get({ url: `/system/medicine-in-out-record/get`, params })
  },

  // 新增药品出入柜操作记录
  createMedicineInOutRecord: async (data: MedicineInOutRecordVO) => {
    return await request.post({ url: `/system/medicine-in-out-record/create`, data })
  },

  // 修改药品出入柜操作记录
  updateMedicineInOutRecord: async (data: MedicineInOutRecordVO) => {
    return await request.put({ url: `/system/medicine-in-out-record/update`, data })
  },

  // 删除药品出入柜操作记录
  deleteMedicineInOutRecord: async (id: number) => {
    return await request.delete({ url: `/system/medicine-in-out-record/delete?id=` + id })
  },

  // 导出药品出入柜操作记录 Excel
  exportMedicineInOutRecord: async (params) => {
    return await request.download({ url: `/system/medicine-in-out-record/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/medicine-in-out-record/get-import-template` })
  },

  // 导入药品出入柜操作记录 Excel
  importMedicineInOutRecord: async (formData) => {
    return await request.upload({ url: `/system/medicine-in-out-record/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/medicine-in-out-record/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}