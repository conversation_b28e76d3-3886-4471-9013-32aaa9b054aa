<template>
  <BasicPage :tabs="['药柜设备信息管理']">
    <template #action>
      <BasicButtonImport perm-code="system:cabinet:import" file-name="药柜设备信息管理"
        :template-api="CabinetApi.importTemplate" :import-api="CabinetApi.importCabinet"
        :exportError-file-api="CabinetApi.exportErrorFile" @success="handlerImportSuccess" />
      <BasicButtonExport perm-code="system:cabinet:export" file-name="药柜设备信息管理" :params="{ ...searchForm }"
        :export-api="CabinetApi.exportCabinet" />
      <el-button v-hasPermi="['system:cabinet:create']" @click="() => handleEdit({ id: null })">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register" />

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { CabinetApi } from '@/api/system/smc/cabinet'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:cabinet:update', callback: handleEdit },
    { label: '查看', permCode: 'system:cabinet:query', callback: handleDetail },
    { label: '删除', permCode: 'system:cabinet:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({

  api: CabinetApi.getCabinetPage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: CabinetApi.getCabinet,
    submitApi: id ? CabinetApi.updateCabinet : CabinetApi.createCabinet,
    afterFetch: (res: any) => {
      // 将字符串格式的部门ID转换为数组格式用于表单显示
      if (res.deptIds && typeof res.deptIds === 'string') {
        res.deptIds = res.deptIds.split(',').map((id: string) => parseInt(id.trim())).filter((id: number) => !isNaN(id))
      }
      return res
    },
    beforeSubmit: (formData: any) => {
      // 将数组格式的部门ID转换为逗号分隔的字符串格式
      if (formData.deptIds && Array.isArray(formData.deptIds)) {
        formData.deptIds = formData.deptIds.join(',')
      }
      return formData
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await CabinetApi.deleteCabinet(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>