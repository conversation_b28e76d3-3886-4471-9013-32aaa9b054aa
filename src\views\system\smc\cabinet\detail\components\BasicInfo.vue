<template>
  <div class="basic-info-container">
    <div class="info-header">
      <h3 class="section-title">基本信息</h3>
      <div class="header-actions">
        <el-button type="primary" @click="showDeptModal = true">
          可使用部门设置
        </el-button>
      </div>
    </div>

    <div class="info-content" v-loading="loading">
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label class="info-label">药柜编号：</label>
            <span class="info-value">{{ cabinetInfo.cabinetCode || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <label class="info-label">存放位置：</label>
            <span class="info-value">{{ cabinetInfo.location || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label class="info-label">IP地址：</label>
            <span class="info-value">{{ cabinetInfo.ip || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <label class="info-label">硬件版本号：</label>
            <span class="info-value">{{ cabinetInfo.hardwareVersion || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label class="info-label">最后心跳时间：</label>
            <span class="info-value">{{ formatDate(cabinetInfo.lastHeartbeat) }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <label class="info-label">在离线状态：</label>
            <el-tag :type="cabinetInfo.onlineStatus === 'online' ? 'success' : 'danger'">
              {{ getOnlineStatusText(cabinetInfo.onlineStatus) }}
            </el-tag>
          </div>
        </el-col>
      </el-row>


      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label class="info-label">创建时间：</label>
            <span class="info-value">{{ formatDate(cabinetInfo.createTime) }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <div class="info-item">
            <label class="info-label">可使用部门：</label>
            <span class="info-value">{{ getDeptNamesDisplay() }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 可使用部门设置弹窗 -->
    <el-dialog v-model="showDeptModal" title="可使用部门设置" width="600px">
      <el-form :model="deptForm" label-width="120px">
        <el-form-item label="可使用部门" required>
          <el-tree-select v-model="deptForm.deptIds" :data="deptOptions" multiple show-checkbox check-strictly
            :render-after-expand="false" placeholder="请选择可使用部门" style="width: 100%"
            :props="{ label: 'name', value: 'id', children: 'children' }" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showDeptModal = false">取消</el-button>
        <el-button type="primary" @click="handleSaveDept" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { DeptApi } from '@/api/system/dept/deptOption'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'

const props = defineProps({
  cabinetInfo: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update-dept'])

const showDeptModal = ref(false)
const saving = ref(false)
const deptOptions = ref<any[]>([])
const deptForm = ref<{
  deptIds: number[]
}>({
  deptIds: []
})

// 获取在线状态文本
const getOnlineStatusText = (status: string) => {
  if (!status) return '未知'
  return getDictLabel(DICT_TYPE.SMC_ONLINE_STATUS, status) || status
}

// 格式化数字显示
const formatNumber = (value: any) => {
  if (value === null || value === undefined || value === '') return '-'
  return typeof value === 'number' ? value.toString() : value
}

// 获取部门列表
const getDeptList = async () => {
  try {
    const response = await DeptApi.getDeptList({})
    console.log('部门列表API响应:', response)

    // 根据实际API响应结构处理数据
    let deptData: any[] = []
    if (response && Array.isArray(response)) {
      deptData = response
    } else if (response && response.data && Array.isArray(response.data)) {
      deptData = response.data
    } else if (response && Array.isArray(response.list)) {
      deptData = response.list
    }

    deptOptions.value = deptData
    console.log('处理后的部门数据:', deptOptions.value)
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 获取部门名称显示
const getDeptNamesDisplay = () => {
  if (!props.cabinetInfo.deptIds || !Array.isArray(props.cabinetInfo.deptIds)) {
    return '-'
  }

  if (deptOptions.value.length === 0) {
    return props.cabinetInfo.deptIdsName || '-'
  }

  // 根据部门ID查找部门名称
  const deptNames = props.cabinetInfo.deptIds
    .map((deptId: number) => {
      const dept = findDeptById(deptId, deptOptions.value)
      return dept ? dept.name : `部门${deptId}`
    })
    .filter(Boolean)

  return deptNames.length > 0 ? deptNames.join('，') : '-'
}

// 递归查找部门
const findDeptById = (id: number, deptList: any[]): any => {
  for (const dept of deptList) {
    if (dept.id === id) {
      return dept
    }
    if (dept.children && dept.children.length > 0) {
      const found = findDeptById(id, dept.children)
      if (found) return found
    }
  }
  return null
}

// 保存部门设置
const handleSaveDept = async () => {
  try {
    saving.value = true
    emit('update-dept', deptForm.value.deptIds)
    showDeptModal.value = false
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 监听弹窗显示，初始化数据
watch(showDeptModal, (visible) => {
  if (visible) {
    deptForm.value.deptIds = [...(props.cabinetInfo.deptIds || [])]
    getDeptList()
  }
})

onMounted(() => {
  getDeptList()
})
</script>

<style scoped lang="scss">
.basic-info-container {
  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .info-content {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      min-height: 32px;

      .info-label {
        width: 120px;
        color: #666;
        font-weight: 500;
        flex-shrink: 0;
      }

      .info-value {
        color: #262626;
        flex: 1;
      }
    }
  }
}
</style>
